<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents a hit record.
 * Assumes global functions ordena(), desordena(), validar_textovacio(), format_numberclean() are available.
 */
class Hit
{
    // --- Attributes ---
    private ?string $id = null; // Stores the 'desordenado' ID
    private ?string $descripcion = null;
    private ?string $requester = null;
    private ?float $pago = null;
    private ?string $nota = null;
    private ?string $fecha = null; // DATETIME format 'YYYY-MM-DD HH:MM:SS'
    private ?int $estado = null; // TINYINT(1) DEFAULT 1
    private ?int $retornado = null; // TINYINT(1) DEFAULT 0

    /**
     * Constructor: Initializes properties with default null values.
     */
    public function __construct()
    {
        $this->id = null;
        $this->descripcion = null;
        $this->requester = null;
        $this->pago = null;
        $this->nota = null;
        $this->fecha = null;
        $this->estado = null;
        $this->retornado = null;
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     * Assumes $data contains raw column names from the database.
     *
     * @param array $data Associative array containing property values.
     * @return self Returns a new instance of Hit.
     * @throws Exception If 'desordena' function is missing or data is invalid.
     */
    public static function construct(array $data): self
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            $objeto = new self();
            // The ID from the DB ('id') is assumed to be the 'ordenado' integer ID.
            $dbId = $data['id'] ?? null;
            if ($dbId !== null) {
                $desordenadoId = desordena((string)$dbId);
                // Add check: Ensure desordena returned a valid, non-empty string ID
                if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                     error_log("desordena() returned empty/null for DB ID: " . $dbId);
                     throw new Exception("Error processing Hit ID during object construction. desordena() failed.");
                }
                $objeto->id = $desordenadoId; // Store the validated desordenado ID
            } else {
                $objeto->id = null; // No ID in data, likely a new object scenario before insert
            }
            
            $objeto->descripcion = $data['descripcion'] ?? null;
            $objeto->requester = $data['requester'] ?? null;
            $objeto->pago = isset($data['pago']) ? (float)$data['pago'] : null;
            $objeto->nota = $data['nota'] ?? null;
            $objeto->fecha = $data['fecha'] ?? null;
            $objeto->estado = isset($data['estado']) ? (int)$data['estado'] : null;
            $objeto->retornado = isset($data['retornado']) ? (int)$data['retornado'] : null;

            return $objeto;

        } catch (Exception $e) {
            // Consider logging the error here
            error_log("Error constructing Hit from data: " . print_r($data, true) . " Error: " . $e->getMessage());
            throw new Exception("Error constructing Hit: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a Hit object from the database by its 'desordenado' string ID.
     *
     * @param string $id       The 'desordenado' string ID of the Hit to retrieve.
     * @param PDO    $conexion The database connection object.
     * @return self|null A Hit object if found, null otherwise.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the provided ID is empty or invalid.
     */
    public static function get(string $id, PDO $conexion): ?self
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert the 'desordenado' string ID to the 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM hits
            WHERE id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $idOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? static::construct($resultado) : null;

        } catch (PDOException $e) {
            error_log("Database error getting Hit (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error fetching Hit: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena or construct
            error_log("Error getting Hit (ID: $id): " . $e->getMessage());
            throw new Exception("Error fetching Hit: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all active Hit objects.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of Hit objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getList(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM hits
            WHERE estado = :estado
            ORDER BY fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            // Assuming 1 means active
            $statement->bindValue(":estado", 1, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing Hit during getList for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Skip this item and log
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting Hit list: " . $e->getMessage());
            throw new Exception("Database error fetching Hit list: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from construct
            error_log("Error getting Hit list: " . $e->getMessage());
            throw new Exception("Error fetching Hit list: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of Hit objects by requester.
     *
     * @param string $requester The requester to filter by.
     * @param PDO $conexion The database connection object.
     * @return array An array of Hit objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getListByRequester(string $requester, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM hits
            WHERE requester = :requester AND estado = :estado
            ORDER BY fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":requester", $requester, PDO::PARAM_STR);
            $statement->bindValue(":estado", 1, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing Hit during getListByRequester for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Skip this item and log
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting Hit list by requester: " . $e->getMessage());
            throw new Exception("Database error fetching Hit list by requester: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from construct
            error_log("Error getting Hit list by requester: " . $e->getMessage());
            throw new Exception("Error fetching Hit list by requester: " . $e->getMessage());
        }
    }

    /**
     * Calculates the sum of 'pago' for an array of Hit objects.
     *
     * @param Hit[] $hits An array of Hit objects.
     * @return float The total sum of 'pago'.
     */
    public static function getSumPago(array $hits): float
    {
        $pagoTotal = 0.0;
        foreach ($hits as $hit) {
            // Use getter to access private property
            if ($hit instanceof self && $hit->getPago() !== null) {
                $pagoTotal += $hit->getPago();
            }
        }
        return $pagoTotal;
    }

    /**
     * Saves (inserts or updates) the current Hit instance to the database.
     * Sets the 'estado' to 1 (active) and 'retornado' to 0 implicitly during insert if not set.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        // Debugging: Log the ID right before determining the operation type
        $currentId = $this->getId();
        error_log("Hit::guardar() called. ID is: " . var_export($currentId, true));

        // Determine if it's an insert or update *before* validation
        $isInsertOperation = ($currentId === null || empty(trim($currentId)));
        error_log("Hit::guardar() - isInsertOperation determined as: " . var_export($isInsertOperation, true));

        // Call validation, passing the correct flag
        $this->validarDatos($isInsertOperation); // Pass true for insert, false for update

        try {
            // Use the pre-determined flag for the operation
            if (!$isInsertOperation) { // It's an update
                return $this->_update($conexion);
            } else { // It's an insert
                return $this->_insert($conexion);
            }
        } catch (PDOException $e) {
            $idInfo = $this->getId() ?? 'N/A';
            error_log("Database error saving Hit (ID: {$idInfo}): " . $e->getMessage());
            throw new Exception("Database error saving Hit: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("General error saving Hit: " . $e->getMessage());
            throw new Exception("Error saving Hit: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current Hit instance into the database. (Private method)
     * Sets estado to 1 (active) and retornado to 0 by default on insert.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'desordena' function is missing.
     */
    private function _insert(PDO $conexion): bool
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        // Assume new hits are active by default
        $estadoParaGuardar = $this->getEstado() ?? 1;
        $retornadoParaGuardar = $this->getRetornado() ?? 0;

        $query = <<<SQL
        INSERT INTO hits (
            descripcion,
            requester,
            pago,
            nota,
            fecha,
            estado,
            retornado
        ) VALUES (
            :descripcion,
            :requester,
            :pago,
            :nota,
            :fecha,
            :estado,
            :retornado
        )
        SQL;

        $statement = $conexion->prepare($query);

        $fechaActual = $this->getFecha() ?? date('Y-m-d H:i:s');

        $statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
        $statement->bindValue(':requester', $this->getRequester(), PDO::PARAM_STR);
        $statement->bindValue(':pago', $this->getPago(), PDO::PARAM_STR);
        $statement->bindValue(':nota', $this->getNota(), PDO::PARAM_STR);
        $statement->bindValue(':fecha', $fechaActual, PDO::PARAM_STR);
        $statement->bindValue(':estado', $estadoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':retornado', $retornadoParaGuardar, PDO::PARAM_INT);

        $success = $statement->execute();

        if ($success) {
            // Get the last inserted ID (which is the 'ordenado' integer ID)
            $lastIdOrdenado = $conexion->lastInsertId();
            if ($lastIdOrdenado) {
                // Convert it to 'desordenado' string ID and set it on the object
                $this->setId(desordena((string)$lastIdOrdenado));
                // Also update the object properties with values actually inserted
                $this->setEstado($estadoParaGuardar);
                $this->setRetornado($retornadoParaGuardar);
                $this->setFecha($fechaActual);
            } else {
                 error_log("Failed to retrieve lastInsertId after Hit insert.");
                 return false;
            }
        } else {
            error_log("Failed to insert Hit: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current Hit instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'ordena' function is missing or ID is invalid.
     */
    private function _update(PDO $conexion): bool
    {
        if ($this->getId() === null || empty(trim($this->getId()))) {
            throw new Exception("Cannot update Hit without a valid ID.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        // Convert internal 'desordenado' string ID to 'ordenado' integer ID for WHERE clause
        $idOrdenado = ordena($this->getId());
         if ($idOrdenado === false || $idOrdenado <= 0) {
             throw new Exception("Failed to process the Hit ID for update: " . $this->getId());
         }

        // Use current estado if set, otherwise default to 1 (active)
        $estadoParaGuardar = $this->getEstado() ?? 1;
        $retornadoParaGuardar = $this->getRetornado() ?? 0;

        $query = <<<SQL
        UPDATE hits SET
             descripcion = :descripcion
            ,requester = :requester
            ,pago = :pago
            ,nota = :nota
            ,fecha = :fecha
            ,estado = :estado
            ,retornado = :retornado
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
        $statement->bindValue(':requester', $this->getRequester(), PDO::PARAM_STR);
        $statement->bindValue(':pago', $this->getPago(), PDO::PARAM_STR);
        $statement->bindValue(':nota', $this->getNota(), PDO::PARAM_STR);
        $statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
        $statement->bindValue(':estado', $estadoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':retornado', $retornadoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update Hit (ID: {$this->getId()} / {$idOrdenado}): " . implode(" | ", $statement->errorInfo()));
        } else {
             // Ensure object's estado reflects the saved state
             $this->setEstado($estadoParaGuardar);
             $this->setRetornado($retornadoParaGuardar);
        }

        return $success;
    }

    /**
     * Deletes (soft deletes) a Hit record by setting its estado to 0.
     *
     * @param string $id       The 'desordenado' string ID of the Hit to delete.
     * @param PDO    $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function delete(string $id, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided for deletion.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID for deletion.");
            }

            $query = <<<SQL
            UPDATE hits
            SET estado = :estado
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':estado', 0, PDO::PARAM_INT); // Set estado to 0 for soft delete
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            error_log("Database error soft-deleting Hit (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error deleting Hit: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena
             error_log("Error soft-deleting Hit (ID: $id): " . $e->getMessage());
             throw new Exception("Error deleting Hit: " . $e->getMessage());
        }
    }

    /**
     * Validates the essential data for the Hit.
     * Assumes global validation/formatting functions exist.
     * Throws an Exception if validation fails.
     *
     * @param bool $isInsert Optional flag to indicate if validation is for an insert operation.
     * @throws Exception If validation fails.
     */
    private function validarDatos(bool $isInsert = false): void
    {
        // Assume global functions exist and handle throwing exceptions on failure
        if (!function_exists('validar_textovacio') || !function_exists('format_numberclean')) {
             throw new Exception("Required global validation/formatting functions are missing.");
        }

        try {
            // Validate descripcion
            if ($this->getDescripcion() !== null) {
                validar_textovacio($this->getDescripcion(), 'La descripción no puede estar vacía');
            }

            // Validate requester
            if ($this->getRequester() !== null) {
                validar_textovacio($this->getRequester(), 'Debe especificar el requester');
            }

            // Validate Pago
            if ($this->getPago() !== null) {
                $pagoOriginal = $this->getPago();
                $pagoLimpio = format_numberclean((string)$pagoOriginal);
                if (!is_numeric($pagoLimpio)) {
                     throw new Exception("El pago proporcionado no es numérico después de la limpieza.");
                }
                // Re-set the value on the object with the cleaned float value
                $this->setPago((float)$pagoLimpio);
            }

            // Validate fecha if provided
            if ($this->getFecha() !== null && !empty(trim($this->getFecha()))) {
                $fecha = $this->getFecha();
                if (!preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $fecha)) {
                    throw new Exception('La fecha debe tener el formato YYYY-MM-DD HH:MM:SS');
                }
            }

            // Validate estado if it's set
            if ($this->getEstado() !== null && !in_array($this->getEstado(), [0, 1], true)) {
                 throw new Exception("El estado no es válido (debe ser 0 o 1).");
            }

            // Validate retornado if it's set
            if ($this->getRetornado() !== null && !in_array($this->getRetornado(), [0, 1], true)) {
                 throw new Exception("El valor retornado no es válido (debe ser 0 o 1).");
            }

        } catch (Exception $e) {
            // Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
            throw new Exception("Validation failed: " . $e->getMessage());
        }
    }

    // --- GETTERS AND SETTERS ---

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Sets the 'desordenado' string ID.
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getDescripcion(): ?string
    {
        return $this->descripcion;
    }

    public function setDescripcion(?string $descripcion): self
    {
        $this->descripcion = $descripcion;
        return $this;
    }

    public function getRequester(): ?string
    {
        return $this->requester;
    }

    public function setRequester(?string $requester): self
    {
        $this->requester = $requester;
        return $this;
    }

    public function getPago(): ?float
    {
        return $this->pago;
    }

    public function setPago(?float $pago): self
    {
        $this->pago = $pago;
        return $this;
    }

    public function getNota(): ?string
    {
        return $this->nota;
    }

    public function setNota(?string $nota): self
    {
        $this->nota = $nota;
        return $this;
    }

    public function getFecha(): ?string
    {
        return $this->fecha;
    }

    public function setFecha(?string $fecha): self
    {
        $this->fecha = $fecha;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    public function getRetornado(): ?int
    {
        return $this->retornado;
    }

    public function setRetornado(?int $retornado): self
    {
        $this->retornado = $retornado;
        return $this;
    }
}
