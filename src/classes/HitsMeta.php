<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents hits meta data.
 * Assumes global functions ordena(), desordena(), validar_textovacio(), format_numberclean() are available.
 */
class HitsMeta
{
    // --- Attributes ---
    private ?string $id = null; // Stores the 'desordenado' ID
    private ?float $valor = null;
    private ?string $fecha = null; // DATE format 'YYYY-MM-DD'

    /**
     * Constructor: Initializes properties with default null values.
     */
    public function __construct()
    {
        $this->id = null;
        $this->valor = null;
        $this->fecha = null;
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     * Assumes $data contains raw column names from the database.
     *
     * @param array $data Associative array containing property values.
     * @return self Returns a new instance of HitsMeta.
     * @throws Exception If 'desordena' function is missing or data is invalid.
     */
    public static function construct(array $data): self
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            $objeto = new self();
            // The ID from the DB ('id') is assumed to be the 'ordenado' integer ID.
            $dbId = $data['id'] ?? null;
            if ($dbId !== null) {
                $desordenadoId = desordena((string)$dbId);
                // Add check: Ensure desordena returned a valid, non-empty string ID
                if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                     error_log("desordena() returned empty/null for DB ID: " . $dbId);
                     throw new Exception("Error processing HitsMeta ID during object construction. desordena() failed.");
                }
                $objeto->id = $desordenadoId; // Store the validated desordenado ID
            } else {
                $objeto->id = null; // No ID in data, likely a new object scenario before insert
            }
            
            $objeto->valor = isset($data['valor']) ? (float)$data['valor'] : null;
            $objeto->fecha = $data['fecha'] ?? null;

            return $objeto;

        } catch (Exception $e) {
            // Consider logging the error here
            error_log("Error constructing HitsMeta from data: " . print_r($data, true) . " Error: " . $e->getMessage());
            throw new Exception("Error constructing HitsMeta: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a HitsMeta object from the database by its 'desordenado' string ID.
     *
     * @param string $id       The 'desordenado' string ID of the HitsMeta to retrieve.
     * @param PDO    $conexion The database connection object.
     * @return self|null A HitsMeta object if found, null otherwise.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the provided ID is empty or invalid.
     */
    public static function get(string $id, PDO $conexion): ?self
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert the 'desordenado' string ID to the 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM hits_meta
            WHERE id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $idOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? static::construct($resultado) : null;

        } catch (PDOException $e) {
            error_log("Database error getting HitsMeta (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error fetching HitsMeta: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena or construct
            error_log("Error getting HitsMeta (ID: $id): " . $e->getMessage());
            throw new Exception("Error fetching HitsMeta: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all HitsMeta objects.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of HitsMeta objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getList(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM hits_meta
            ORDER BY fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing HitsMeta during getList for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Skip this item and log
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting HitsMeta list: " . $e->getMessage());
            throw new Exception("Database error fetching HitsMeta list: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from construct
            error_log("Error getting HitsMeta list: " . $e->getMessage());
            throw new Exception("Error fetching HitsMeta list: " . $e->getMessage());
        }
    }

    /**
     * Calculates the sum of 'valor' for an array of HitsMeta objects.
     *
     * @param HitsMeta[] $hitsMetas An array of HitsMeta objects.
     * @return float The total sum of 'valor'.
     */
    public static function getSumValor(array $hitsMetas): float
    {
        $valorTotal = 0.0;
        foreach ($hitsMetas as $hitsMeta) {
            // Use getter to access private property
            if ($hitsMeta instanceof self && $hitsMeta->getValor() !== null) {
                $valorTotal += $hitsMeta->getValor();
            }
        }
        return $valorTotal;
    }

    /**
     * Saves (inserts or updates) the current HitsMeta instance to the database.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        // Debugging: Log the ID right before determining the operation type
        $currentId = $this->getId();
        error_log("HitsMeta::guardar() called. ID is: " . var_export($currentId, true));

        // Determine if it's an insert or update *before* validation
        $isInsertOperation = ($currentId === null || empty(trim($currentId)));
        error_log("HitsMeta::guardar() - isInsertOperation determined as: " . var_export($isInsertOperation, true));

        // Call validation, passing the correct flag
        $this->validarDatos($isInsertOperation); // Pass true for insert, false for update

        try {
            // Use the pre-determined flag for the operation
            if (!$isInsertOperation) { // It's an update
                return $this->_update($conexion);
            } else { // It's an insert
                return $this->_insert($conexion);
            }
        } catch (PDOException $e) {
            $idInfo = $this->getId() ?? 'N/A';
            error_log("Database error saving HitsMeta (ID: {$idInfo}): " . $e->getMessage());
            throw new Exception("Database error saving HitsMeta: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("General error saving HitsMeta: " . $e->getMessage());
            throw new Exception("Error saving HitsMeta: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current HitsMeta instance into the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'desordena' function is missing.
     */
    private function _insert(PDO $conexion): bool
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        $query = <<<SQL
        INSERT INTO hits_meta (
            valor,
            fecha
        ) VALUES (
            :valor,
            :fecha
        )
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
        $statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);

        $success = $statement->execute();

        if ($success) {
            // Get the last inserted ID (which is the 'ordenado' integer ID)
            $lastIdOrdenado = $conexion->lastInsertId();
            if ($lastIdOrdenado) {
                // Convert it to 'desordenado' string ID and set it on the object
                $this->setId(desordena((string)$lastIdOrdenado));
            } else {
                 error_log("Failed to retrieve lastInsertId after HitsMeta insert.");
                 return false;
            }
        } else {
            error_log("Failed to insert HitsMeta: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current HitsMeta instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'ordena' function is missing or ID is invalid.
     */
    private function _update(PDO $conexion): bool
    {
        if ($this->getId() === null || empty(trim($this->getId()))) {
            throw new Exception("Cannot update HitsMeta without a valid ID.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        // Convert internal 'desordenado' string ID to 'ordenado' integer ID for WHERE clause
        $idOrdenado = ordena($this->getId());
         if ($idOrdenado === false || $idOrdenado <= 0) {
             throw new Exception("Failed to process the HitsMeta ID for update: " . $this->getId());
         }

        $query = <<<SQL
        UPDATE hits_meta SET
             valor = :valor
            ,fecha = :fecha
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
        $statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
        $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update HitsMeta (ID: {$this->getId()} / {$idOrdenado}): " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Deletes a HitsMeta record from the database.
     *
     * @param string $id       The 'desordenado' string ID of the HitsMeta to delete.
     * @param PDO    $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function delete(string $id, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided for deletion.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID for deletion.");
            }

            $query = <<<SQL
            DELETE FROM hits_meta
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            error_log("Database error deleting HitsMeta (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error deleting HitsMeta: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena
             error_log("Error deleting HitsMeta (ID: $id): " . $e->getMessage());
             throw new Exception("Error deleting HitsMeta: " . $e->getMessage());
        }
    }

    /**
     * Validates the essential data for the HitsMeta.
     * Assumes global validation/formatting functions exist.
     * Throws an Exception if validation fails.
     *
     * @param bool $isInsert Optional flag to indicate if validation is for an insert operation.
     * @throws Exception If validation fails.
     */
    private function validarDatos(bool $isInsert = false): void
    {
        // Assume global functions exist and handle throwing exceptions on failure
        if (!function_exists('format_numberclean')) {
             throw new Exception("Required global validation/formatting functions are missing.");
        }

        try {
            // Validate Valor
            $valorOriginal = $this->getValor();
            if ($valorOriginal === null) {
                 throw new Exception('Debe especificar el valor');
            }

            // If not null, proceed to clean and validate numeric format
            $valorLimpio = format_numberclean((string)$valorOriginal);
            if (!is_numeric($valorLimpio)) {
                 throw new Exception("El valor proporcionado no es numérico después de la limpieza.");
            }
            // Re-set the value on the object with the cleaned float value
            $this->setValor((float)$valorLimpio);

            // Validate fecha
            if ($this->getFecha() === null || empty(trim($this->getFecha()))) {
                throw new Exception('Debe especificar la fecha');
            }

            // Basic date format validation (YYYY-MM-DD)
            $fecha = $this->getFecha();
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha)) {
                throw new Exception('La fecha debe tener el formato YYYY-MM-DD');
            }

        } catch (Exception $e) {
            // Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
            throw new Exception("Validation failed: " . $e->getMessage());
        }
    }

    // --- GETTERS AND SETTERS ---

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Sets the 'desordenado' string ID.
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getValor(): ?float
    {
        return $this->valor;
    }

    public function setValor(?float $valor): self
    {
        $this->valor = $valor;
        return $this;
    }

    public function getFecha(): ?string
    {
        return $this->fecha;
    }

    public function setFecha(?string $fecha): self
    {
        $this->fecha = $fecha;
        return $this;
    }
}
