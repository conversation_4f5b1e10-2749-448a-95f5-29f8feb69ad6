<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents requester notes for hits.
 * Assumes global functions ordena(), desordena(), validar_textovacio() are available.
 */
class HitsRequesterNotas
{
    // --- Attributes ---
    private ?string $id = null; // Stores the 'desordenado' ID
    private ?string $requester = null;
    private ?string $nota = null;

    /**
     * Constructor: Initializes properties with default null values.
     */
    public function __construct()
    {
        $this->id = null;
        $this->requester = null;
        $this->nota = null;
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     * Assumes $data contains raw column names from the database.
     *
     * @param array $data Associative array containing property values.
     * @return self Returns a new instance of HitsRequesterNotas.
     * @throws Exception If 'desordena' function is missing or data is invalid.
     */
    public static function construct(array $data): self
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            $objeto = new self();
            // The ID from the DB ('id') is assumed to be the 'ordenado' integer ID.
            $dbId = $data['id'] ?? null;
            if ($dbId !== null) {
                $desordenadoId = desordena((string)$dbId);
                // Add check: Ensure desordena returned a valid, non-empty string ID
                if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                     error_log("desordena() returned empty/null for DB ID: " . $dbId);
                     throw new Exception("Error processing HitsRequesterNotas ID during object construction. desordena() failed.");
                }
                $objeto->id = $desordenadoId; // Store the validated desordenado ID
            } else {
                $objeto->id = null; // No ID in data, likely a new object scenario before insert
            }
            
            $objeto->requester = $data['requester'] ?? null;
            $objeto->nota = $data['nota'] ?? null;

            return $objeto;

        } catch (Exception $e) {
            // Consider logging the error here
            error_log("Error constructing HitsRequesterNotas from data: " . print_r($data, true) . " Error: " . $e->getMessage());
            throw new Exception("Error constructing HitsRequesterNotas: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a HitsRequesterNotas object from the database by its 'desordenado' string ID.
     *
     * @param string $id       The 'desordenado' string ID of the HitsRequesterNotas to retrieve.
     * @param PDO    $conexion The database connection object.
     * @return self|null A HitsRequesterNotas object if found, null otherwise.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the provided ID is empty or invalid.
     */
    public static function get(string $id, PDO $conexion): ?self
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert the 'desordenado' string ID to the 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM hits_requester_notas
            WHERE id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $idOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? static::construct($resultado) : null;

        } catch (PDOException $e) {
            error_log("Database error getting HitsRequesterNotas (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error fetching HitsRequesterNotas: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena or construct
            error_log("Error getting HitsRequesterNotas (ID: $id): " . $e->getMessage());
            throw new Exception("Error fetching HitsRequesterNotas: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all HitsRequesterNotas objects.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of HitsRequesterNotas objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getList(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM hits_requester_notas
            ORDER BY requester
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing HitsRequesterNotas during getList for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Skip this item and log
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting HitsRequesterNotas list: " . $e->getMessage());
            throw new Exception("Database error fetching HitsRequesterNotas list: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from construct
            error_log("Error getting HitsRequesterNotas list: " . $e->getMessage());
            throw new Exception("Error fetching HitsRequesterNotas list: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a HitsRequesterNotas object by requester name.
     *
     * @param string $requester The requester name to search for.
     * @param PDO    $conexion The database connection object.
     * @return self|null A HitsRequesterNotas object if found, null otherwise.
     * @throws Exception If DB error occurs.
     * @throws InvalidArgumentException If the requester is empty or invalid.
     */
    public static function getByRequester(string $requester, PDO $conexion): ?self
    {
        if (empty(trim($requester))) {
            throw new InvalidArgumentException("Invalid requester provided.");
        }

        try {
            $query = <<<SQL
            SELECT *
            FROM hits_requester_notas
            WHERE requester = :requester
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":requester", $requester, PDO::PARAM_STR);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? static::construct($resultado) : null;

        } catch (PDOException $e) {
            error_log("Database error getting HitsRequesterNotas by requester ($requester): " . $e->getMessage());
            throw new Exception("Database error fetching HitsRequesterNotas by requester: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from construct
            error_log("Error getting HitsRequesterNotas by requester ($requester): " . $e->getMessage());
            throw new Exception("Error fetching HitsRequesterNotas by requester: " . $e->getMessage());
        }
    }

    /**
     * Saves (inserts or updates) the current HitsRequesterNotas instance to the database.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        // Debugging: Log the ID right before determining the operation type
        $currentId = $this->getId();
        error_log("HitsRequesterNotas::guardar() called. ID is: " . var_export($currentId, true));

        // Determine if it's an insert or update *before* validation
        $isInsertOperation = ($currentId === null || empty(trim($currentId)));
        error_log("HitsRequesterNotas::guardar() - isInsertOperation determined as: " . var_export($isInsertOperation, true));

        // Call validation, passing the correct flag
        $this->validarDatos($isInsertOperation); // Pass true for insert, false for update

        try {
            // Use the pre-determined flag for the operation
            if (!$isInsertOperation) { // It's an update
                return $this->_update($conexion);
            } else { // It's an insert
                return $this->_insert($conexion);
            }
        } catch (PDOException $e) {
            $idInfo = $this->getId() ?? 'N/A';
            error_log("Database error saving HitsRequesterNotas (ID: {$idInfo}): " . $e->getMessage());
            throw new Exception("Database error saving HitsRequesterNotas: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("General error saving HitsRequesterNotas: " . $e->getMessage());
            throw new Exception("Error saving HitsRequesterNotas: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current HitsRequesterNotas instance into the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'desordena' function is missing.
     */
    private function _insert(PDO $conexion): bool
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        $query = <<<SQL
        INSERT INTO hits_requester_notas (
            requester,
            nota
        ) VALUES (
            :requester,
            :nota
        )
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':requester', $this->getRequester(), PDO::PARAM_STR);
        $statement->bindValue(':nota', $this->getNota(), PDO::PARAM_STR);

        $success = $statement->execute();

        if ($success) {
            // Get the last inserted ID (which is the 'ordenado' integer ID)
            $lastIdOrdenado = $conexion->lastInsertId();
            if ($lastIdOrdenado) {
                // Convert it to 'desordenado' string ID and set it on the object
                $this->setId(desordena((string)$lastIdOrdenado));
            } else {
                 error_log("Failed to retrieve lastInsertId after HitsRequesterNotas insert.");
                 return false;
            }
        } else {
            error_log("Failed to insert HitsRequesterNotas: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current HitsRequesterNotas instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'ordena' function is missing or ID is invalid.
     */
    private function _update(PDO $conexion): bool
    {
        if ($this->getId() === null || empty(trim($this->getId()))) {
            throw new Exception("Cannot update HitsRequesterNotas without a valid ID.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        // Convert internal 'desordenado' string ID to 'ordenado' integer ID for WHERE clause
        $idOrdenado = ordena($this->getId());
         if ($idOrdenado === false || $idOrdenado <= 0) {
             throw new Exception("Failed to process the HitsRequesterNotas ID for update: " . $this->getId());
         }

        $query = <<<SQL
        UPDATE hits_requester_notas SET
             requester = :requester
            ,nota = :nota
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':requester', $this->getRequester(), PDO::PARAM_STR);
        $statement->bindValue(':nota', $this->getNota(), PDO::PARAM_STR);
        $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update HitsRequesterNotas (ID: {$this->getId()} / {$idOrdenado}): " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Deletes a HitsRequesterNotas record from the database.
     *
     * @param string $id       The 'desordenado' string ID of the HitsRequesterNotas to delete.
     * @param PDO    $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function delete(string $id, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided for deletion.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID for deletion.");
            }

            $query = <<<SQL
            DELETE FROM hits_requester_notas
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            error_log("Database error deleting HitsRequesterNotas (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error deleting HitsRequesterNotas: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena
             error_log("Error deleting HitsRequesterNotas (ID: $id): " . $e->getMessage());
             throw new Exception("Error deleting HitsRequesterNotas: " . $e->getMessage());
        }
    }

    /**
     * Validates the essential data for the HitsRequesterNotas.
     * Assumes global validation functions exist.
     * Throws an Exception if validation fails.
     *
     * @param bool $isInsert Optional flag to indicate if validation is for an insert operation.
     * @throws Exception If validation fails.
     */
    private function validarDatos(bool $isInsert = false): void
    {
        // Assume global functions exist and handle throwing exceptions on failure
        if (!function_exists('validar_textovacio')) {
             throw new Exception("Required global validation functions are missing.");
        }

        try {
            // Validate requester
            validar_textovacio($this->getRequester(), 'Debe especificar el requester');

            // Validate nota (optional validation - can be empty)
            // If you want to enforce nota to not be empty, uncomment the line below:
            // validar_textovacio($this->getNota(), 'Debe especificar la nota');

        } catch (Exception $e) {
            // Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
            throw new Exception("Validation failed: " . $e->getMessage());
        }
    }

    // --- GETTERS AND SETTERS ---

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Sets the 'desordenado' string ID.
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getRequester(): ?string
    {
        return $this->requester;
    }

    public function setRequester(?string $requester): self
    {
        $this->requester = $requester;
        return $this;
    }

    public function getNota(): ?string
    {
        return $this->nota;
    }

    public function setNota(?string $nota): self
    {
        $this->nota = $nota;
        return $this;
    }
}
